<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ad-Link Bypasser Test Page</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-link {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        .test-link:hover {
            background-color: #0056b3;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .keyboard-shortcut {
            background-color: #e9ecef;
            padding: 5px 10px;
            border-radius: 3px;
            font-family: monospace;
            font-weight: bold;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .status.checking {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status.active {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.inactive {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Universal Ad-Link Bypasser</h1>
        
        <div id="script-status" class="status checking">
            <strong>Checking script status...</strong>
        </div>

        <div class="info">
            <h3>📋 How to Test</h3>
            <p>This page helps you verify that the Ad-Link Bypasser userscript is working correctly. The script should automatically detect when you visit supported ad-link shortener domains and bypass them.</p>
        </div>

        <div class="warning">
            <h3>⚠️ Important Notes</h3>
            <ul>
                <li>Make sure you have installed the userscript in Violentmonkey or Tampermonkey</li>
                <li>The script only works on actual ad-link shortener domains, not this test page</li>
                <li>Use the manual trigger <span class="keyboard-shortcut">Ctrl+Shift+B</span> if automatic bypass fails</li>
                <li>Check browser console (F12) for debug information if needed</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🎯 Supported Services</h3>
            <p>The script supports 40+ ad-link shortener services including:</p>
            <ul class="feature-list">
                <li>Linkvertise (linkvertise.com, linkvertise.net)</li>
                <li>Lootbox (lootbox.to, lootdest.info)</li>
                <li>AdFly (adfly.com, adf.ly)</li>
                <li>Shorte.st (shorte.st, sh.st)</li>
                <li>Ouo.io (ouo.io, ouo.press)</li>
                <li>Boost.ink and BoostInk.net</li>
                <li>Sub2Unlock services</li>
                <li>And many more...</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>⚡ Features</h3>
            <ul class="feature-list">
                <li>Automatic bypass detection and redirection</li>
                <li>Smart retry mechanism with fallback methods</li>
                <li>Popup and overlay removal</li>
                <li>Timer skipping capabilities</li>
                <li>User-friendly notifications</li>
                <li>Manual trigger support</li>
                <li>Cross-browser compatibility</li>
                <li>Regular updates and maintenance</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔧 Manual Controls</h3>
            <p>You can manually trigger the bypass process on any page:</p>
            <div style="text-align: center; margin: 20px 0;">
                <button onclick="triggerManualBypass()" class="test-link" style="background-color: #28a745;">
                    🔄 Test Manual Trigger
                </button>
            </div>
            <p><em>Or use the keyboard shortcut: <span class="keyboard-shortcut">Ctrl+Shift+B</span></em></p>
        </div>

        <div class="success">
            <h3>✅ Installation Verification</h3>
            <p>If you can see this page and the script status above shows "Active", your installation is successful! The script will automatically work when you visit supported ad-link shortener URLs.</p>
        </div>

        <div class="info">
            <h3>📚 Need Help?</h3>
            <p>Check the README.md file for detailed installation instructions, troubleshooting tips, and configuration options. If you encounter any issues, enable debug mode and check the browser console for detailed logs.</p>
        </div>
    </div>

    <script>
        // Check if the userscript is loaded
        function checkScriptStatus() {
            const statusDiv = document.getElementById('script-status');
            
            // Check for userscript indicators
            const hasViolentmonkey = typeof GM_info !== 'undefined' || window.VM !== undefined;
            const hasTampermonkey = typeof GM_info !== 'undefined' || window.TM !== undefined;
            const hasUserscript = hasViolentmonkey || hasTampermonkey;
            
            if (hasUserscript) {
                statusDiv.className = 'status active';
                statusDiv.innerHTML = '<strong>✅ Userscript Manager Detected!</strong><br>The Ad-Link Bypasser should be working if properly installed.';
            } else {
                statusDiv.className = 'status inactive';
                statusDiv.innerHTML = '<strong>❌ No Userscript Manager Detected</strong><br>Please install Violentmonkey or Tampermonkey first.';
            }
        }

        // Manual bypass trigger for testing
        function triggerManualBypass() {
            // Simulate the keyboard shortcut
            const event = new KeyboardEvent('keydown', {
                key: 'B',
                ctrlKey: true,
                shiftKey: true,
                bubbles: true
            });
            document.dispatchEvent(event);
            
            alert('Manual bypass triggered! Check console for any debug messages.');
        }

        // Check status when page loads
        document.addEventListener('DOMContentLoaded', checkScriptStatus);
        
        // Also check after a short delay in case userscript loads later
        setTimeout(checkScriptStatus, 1000);
    </script>
</body>
</html>
