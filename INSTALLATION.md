# Quick Installation Guide

## Step 1: Install a Userscript Manager

Choose one of these browser extensions:

### Violentmonkey (Recommended)
- **Chrome**: [Install from Chrome Web Store](https://chrome.google.com/webstore/detail/violentmonkey/jinjaccalgkegednnccohejagnlnfdag)
- **Firefox**: [Install from Firefox Add-ons](https://addons.mozilla.org/en-US/firefox/addon/violentmonkey/)
- **Edge**: [Install from Edge Add-ons](https://microsoftedge.microsoft.com/addons/detail/violentmonkey/eeagobfjdenkkddmbclomhiblgggliao)

### Tampermonkey (Alternative)
- **Chrome**: [Install from Chrome Web Store](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
- **Firefox**: [Install from Firefox Add-ons](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/)
- **Safari**: [Install from App Store](https://apps.apple.com/us/app/tampermonkey/id1482490089)

## Step 2: Install the Ad-Link Bypasser Script

### Method 1: Direct Installation (Easiest)
1. Click on the `ad-link-bypasser.user.js` file in this repository
2. Click the "Raw" button to view the raw script content
3. Your userscript manager should automatically detect the script and show an installation dialog
4. Click "Install" or "Confirm Installation"

### Method 2: Manual Installation
1. Copy the entire content of the `ad-link-bypasser.user.js` file
2. Open your userscript manager dashboard:
   - **Violentmonkey**: Click the extension icon → "Open Dashboard"
   - **Tampermonkey**: Click the extension icon → "Dashboard"
3. Click "Create a new script" or the "+" button
4. Delete any existing content and paste the copied script
5. Press Ctrl+S (or Cmd+S on Mac) to save

## Step 3: Verify Installation

1. Check that the script appears in your userscript manager's dashboard
2. Ensure the script is enabled (toggle should be ON/green)
3. Visit any supported ad-link shortener to test

## Step 4: Test the Script

Try visiting one of these test URLs to see if the script works:
- Any Linkvertise link
- Any AdFly link
- Any Shorte.st link

The script should automatically redirect you to the final destination and show a notification.

## Troubleshooting Installation

### Script not appearing in dashboard
- Make sure you copied the entire script content
- Check that the script starts with `// ==UserScript==`
- Verify your userscript manager is properly installed and enabled

### Script installed but not working
- Check if the script is enabled in your dashboard
- Refresh the page you're testing on
- Try the manual trigger: Ctrl+Shift+B

### Browser permissions
Some browsers may require additional permissions:
1. Go to your browser's extension settings
2. Find your userscript manager
3. Ensure it has permission to "Access your data on all websites"

## Quick Start Commands

After installation, you can:
- **Auto-bypass**: Just visit any supported ad-link URL
- **Manual trigger**: Press Ctrl+Shift+B on any page
- **View logs**: Open browser console (F12) and enable debug mode

## Need Help?

If you encounter any issues:
1. Check the main README.md for detailed troubleshooting
2. Enable debug mode in the script configuration
3. Open an issue on GitHub with your browser and error details

---

**Total installation time**: Less than 2 minutes!
