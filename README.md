# Universal Ad-Link Bypasser

A comprehensive Violentmonkey/Tampermonkey userscript that automatically bypasses popular ad-link shortener services, saving you time and eliminating the need to interact with ads, timers, and captchas.

## 🚀 Features

- **Automatic Bypass**: Instantly redirects to the final destination URL without user interaction
- **Wide Compatibility**: Supports 40+ popular ad-link shortener services
- **Smart Detection**: Automatically detects the service type and applies the appropriate bypass method
- **Error Handling**: Robust retry mechanism with fallback methods
- **User-Friendly**: Shows notifications for successful bypasses and failures
- **Popup Blocker**: Automatically removes annoying overlays and popups
- **Timer Skipper**: Attempts to skip countdown timers
- **Manual Trigger**: Keyboard shortcut (Ctrl+Shift+B) for manual bypass attempts

## 📋 Supported Services

### Primary Targets
- **Linkvertise** (linkvertise.com, linkvertise.net, linkvertise.download)
- **Lootbox** (lootbox.to, lootdest.info, lootdest.org, lootdest.com)
- **AdFly** (adfly.com, adf.ly)
- **Shorte.st** (shorte.st, sh.st)
- **Ouo.io** (ouo.io, ouo.press)

### Additional Services
- Boost.ink / BoostInk.net
- Sub2Unlock.net / Sub2Unlock.com
- MBoost.me
- Rekonise.com
- LetsBoost.net
- SocialWolvez.com
- Work.ink / WorkInk.net
- FC.lc / FC-LC.com
- Cut-URLs.com
- Exe.io / Exey.io
- Sub2Get.com
- Sub4Unlock.io
- EarNow.online
- GTAModding.com / GTAModding.net
- GTAAll.com / GTAAll.net
- ModsFire.com
- ModsBase.com
- Up-Load.io
- Upload.ee
- DDownload.com
- DBree.org
- KrakenFiles.com
- 1Fichier.com
- FileCrypt.cc / FileCrypt.co

## 🔧 Installation

### Prerequisites
You need a userscript manager extension installed in your browser:

- **Violentmonkey** (Recommended) - [Chrome](https://chrome.google.com/webstore/detail/violentmonkey/jinjaccalgkegednnccohejagnlnfdag) | [Firefox](https://addons.mozilla.org/en-US/firefox/addon/violentmonkey/)
- **Tampermonkey** - [Chrome](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo) | [Firefox](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/)
- **Greasemonkey** (Firefox only) - [Firefox](https://addons.mozilla.org/en-US/firefox/addon/greasemonkey/)

### Installation Steps

1. **Install the userscript manager** of your choice from the links above
2. **Download the script**:
   - Click on `ad-link-bypasser.user.js` in this repository
   - Click the "Raw" button to view the raw script
   - Your userscript manager should automatically detect and prompt you to install it
3. **Alternative installation**:
   - Copy the entire content of `ad-link-bypasser.user.js`
   - Open your userscript manager dashboard
   - Create a new script and paste the content
   - Save the script

## 🎯 Usage

### Automatic Operation
The script works automatically once installed. Simply visit any supported ad-link shortener URL, and the script will:

1. Detect the service type
2. Apply the appropriate bypass method
3. Automatically redirect you to the final destination
4. Show a notification confirming the bypass

### Manual Trigger
If the automatic bypass fails or you want to retry:
- Press **Ctrl+Shift+B** to manually trigger the bypass process

### Configuration
You can modify the script's behavior by editing the `CONFIG` object at the top of the script:

```javascript
const CONFIG = {
    debug: false,           // Enable debug logging
    maxRetries: 3,          // Maximum retry attempts
    retryDelay: 2000,       // Delay between retries (ms)
    timeout: 30000,         // Timeout for operations (ms)
    showNotifications: true // Show bypass notifications
};
```

## 🛠️ How It Works

### Bypass Methods

1. **Direct URL Extraction**: Searches page source for embedded destination URLs
2. **API Bypass**: Attempts to use service APIs to get direct links
3. **DOM Manipulation**: Finds and clicks appropriate buttons/links
4. **Form Submission**: Automatically submits forms when necessary
5. **Timer Skipping**: Attempts to bypass countdown timers
6. **Generic Fallback**: Universal method for unknown services

### Technical Features

- **Smart Retry Logic**: Automatically retries failed attempts with exponential backoff
- **Error Handling**: Comprehensive error catching and logging
- **Performance Optimized**: Minimal resource usage and fast execution
- **Cross-Browser Compatible**: Works with all major browsers
- **Regular Updates**: Easily updatable through userscript managers

## 🔍 Troubleshooting

### Common Issues

**Script not working on a specific site:**
- Check if the site is in the supported list
- Try the manual trigger (Ctrl+Shift+B)
- Enable debug mode to see detailed logs

**Bypass fails repeatedly:**
- The site may have updated their protection
- Try refreshing the page and waiting a few seconds
- Some sites require multiple attempts

**Notifications not showing:**
- Check if notifications are enabled in your browser
- Verify the userscript manager has notification permissions

### Debug Mode
Enable debug mode by setting `CONFIG.debug = true` in the script. This will show detailed logs in the browser console (F12 → Console).

## ⚖️ Legal Disclaimer

This script is provided for educational and research purposes only. Users are responsible for ensuring their use complies with:

- Terms of service of the websites they visit
- Local laws and regulations
- Copyright and intellectual property rights

The authors do not encourage or condone bypassing legitimate advertising or violating terms of service.

## 🤝 Contributing

Contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Test your changes thoroughly
4. Submit a pull request with a clear description

### Adding New Services
To add support for a new service:

1. Add the domain to the `@match` directives in the script header
2. Create a new bypass method in the `bypassMethods` object
3. Add the service detection logic in the `attemptBypass` function
4. Test thoroughly and submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔄 Updates

The script includes automatic update functionality. Your userscript manager will check for updates periodically and notify you when new versions are available.

**Current Version**: 2.1.0

### Changelog
- **v2.1.0**: Added support for 40+ services, enhanced bypass methods, popup blocker
- **v2.0.0**: Complete rewrite with modern JavaScript, improved reliability
- **v1.x**: Initial releases with basic bypass functionality

## 📞 Support

If you encounter issues or have questions:

1. Check the troubleshooting section above
2. Enable debug mode and check console logs
3. Open an issue on GitHub with detailed information
4. Include the specific URL that's not working and any error messages

---

**⚠️ Important**: Always respect website terms of service and applicable laws. This tool should be used responsibly and ethically.
