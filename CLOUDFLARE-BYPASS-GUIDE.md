# Cloudflare Bypass Guide for Ad-Link Services

## 🛡️ Understanding Cloudflare Protection

Many ad-link shortener services (like Linkvertise) use Cloudflare's security features to prevent automated bypassing. When you see a page saying "Sorry, you have been blocked" or "Checking your browser," you're encountering Cloudflare's DDoS protection.

## 🔧 Solutions and Workarounds

### Method 1: Use Both Scripts Together

Install both userscripts for maximum effectiveness:

1. **Main Script**: `ad-link-bypasser.user.js` - Handles the actual bypassing
2. **Helper Script**: `cloudflare-bypass-helper.user.js` - Specifically handles Cloudflare challenges

### Method 2: Manual Cloudflare Bypass

When you encounter a Cloudflare challenge:

1. **Wait for the challenge to complete** (usually 5-10 seconds)
2. **Don't refresh the page** during the challenge
3. **Let the automatic redirect happen**
4. **Press Ctrl+Shift+B** to manually trigger the bypass after the challenge completes

### Method 3: Browser Configuration

Configure your browser to appear more "human-like":

#### Chrome/Edge:
```
1. Go to chrome://settings/content/javascript
2. Ensure JavaScript is enabled
3. Go to chrome://settings/privacy
4. Set "Cookies and other site data" to "Allow all cookies"
5. Disable any aggressive ad blockers temporarily
```

#### Firefox:
```
1. Go to about:config
2. Set network.http.referer.XOriginPolicy to 0
3. Set privacy.resistFingerprinting to false
4. Ensure JavaScript is enabled
```

### Method 4: Alternative Bypass Techniques

#### Using Browser Developer Tools:
1. Press F12 to open Developer Tools
2. Go to the Console tab
3. Wait for the Cloudflare challenge to complete
4. Look for any redirect URLs in the Network tab
5. Manually navigate to the final URL

#### Using Incognito/Private Mode:
- Sometimes Cloudflare challenges are easier to pass in incognito mode
- Clear cookies and try again in a private window

## 🚀 Enhanced Script Features

The updated main script now includes:

### Cloudflare Detection
```javascript
// Automatically detects Cloudflare challenges
if (utils.detectCloudflareChallenge()) {
    utils.notify('Cloudflare protection detected - waiting for completion...');
    // Waits for challenge completion
}
```

### Smart Waiting
```javascript
// Waits up to 30 seconds for challenge completion
const challengePassed = await utils.waitForCloudflareBypass();
```

### Retry Logic
```javascript
// Automatically retries bypass after Cloudflare completion
if (challengePassed) {
    setTimeout(attemptBypass, 2000);
}
```

## 🛠️ Troubleshooting Common Issues

### Issue 1: "Checking your browser" loop
**Solution**: 
- Clear browser cookies for the site
- Disable VPN if using one
- Try a different browser or incognito mode

### Issue 2: Bypass fails after Cloudflare completion
**Solution**:
- Wait 5-10 seconds after the challenge completes
- Press Ctrl+Shift+B to manually trigger bypass
- Check browser console for error messages

### Issue 3: Constant Cloudflare challenges
**Solution**:
- Your IP might be flagged - try using a different network
- Disable aggressive browser extensions
- Use a residential VPN instead of datacenter VPN

### Issue 4: Script not detecting Cloudflare completion
**Solution**:
- Enable debug mode in the script
- Check console logs for detection issues
- Manually refresh the page after challenge completion

## 📋 Step-by-Step Bypass Process

### For Linkvertise with Cloudflare:

1. **Visit the Linkvertise URL**
2. **Wait for Cloudflare challenge** (don't interact, let it complete automatically)
3. **Look for the "Checking your browser" message to disappear**
4. **Wait for automatic redirect** to the actual Linkvertise page
5. **The script will automatically attempt bypass** once on the real page
6. **If bypass fails, press Ctrl+Shift+B** for manual trigger

### For Other Protected Services:

1. **Follow the same Cloudflare completion process**
2. **The script will detect the service type** and apply appropriate bypass
3. **Multiple retry attempts** will be made automatically
4. **Manual trigger available** if automatic bypass fails

## 🔍 Debug Mode Instructions

To enable detailed logging:

1. **Edit the script configuration**:
```javascript
const CONFIG = {
    debug: true,  // Change this to true
    // ... other settings
};
```

2. **Open browser console** (F12 → Console tab)
3. **Watch for detailed log messages** during bypass attempts
4. **Share console logs** when reporting issues

## ⚡ Performance Tips

### Optimize for Success:
- **Use a clean browser profile** without many extensions
- **Ensure stable internet connection**
- **Don't use aggressive ad blockers** on shortener sites
- **Allow JavaScript and cookies** for the sites
- **Be patient** - some challenges take time to complete

### Browser Recommendations:
1. **Chrome/Chromium** - Best compatibility
2. **Firefox** - Good alternative with proper configuration
3. **Edge** - Works well with Violentmonkey
4. **Avoid Tor Browser** - Often blocked by Cloudflare

## 🔄 Alternative Services

If a particular service consistently fails:

### Direct Link Extractors:
- Some online tools can extract direct links
- Use at your own risk and verify safety

### Community Bypass Lists:
- Check for community-maintained bypass lists
- Some services have known direct link patterns

## 📞 Getting Help

If you're still having issues:

1. **Enable debug mode** and check console logs
2. **Try the helper script** in addition to the main script
3. **Test in incognito mode** to rule out extension conflicts
4. **Report specific URLs** that aren't working
5. **Include browser and OS information** when asking for help

## ⚖️ Legal and Ethical Considerations

- **Respect terms of service** of the websites you visit
- **Don't abuse bypass tools** for malicious purposes
- **Support content creators** when possible through legitimate means
- **Use responsibly** and in accordance with local laws

---

**Remember**: Cloudflare protection is constantly evolving. What works today might need updates tomorrow. The scripts are designed to adapt, but manual intervention may sometimes be necessary.
