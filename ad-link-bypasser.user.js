// ==UserScript==
// @name         Universal Ad-Link Bypasser
// @namespace    https://github.com/ad-link-bypasser
// @version      2.1.0
// @description  Automatically bypass popular ad-link shortener services including Linkvertise, Lootbox, AdFly, and more
// <AUTHOR>
// @match        *://*.linkvertise.com/*
// @match        *://*.linkvertise.net/*
// @match        *://*.linkvertise.download/*
// @match        *://*.lootbox.to/*
// @match        *://*.lootdest.info/*
// @match        *://*.lootdest.org/*
// @match        *://*.lootdest.com/*
// @match        *://*.adfly.com/*
// @match        *://*.adf.ly/*
// @match        *://*.shorte.st/*
// @match        *://*.sh.st/*
// @match        *://*.ouo.io/*
// @match        *://*.ouo.press/*
// @match        *://*.boost.ink/*
// @match        *://*.boostink.net/*
// @match        *://*.sub2unlock.net/*
// @match        *://*.sub2unlock.com/*
// @match        *://*.mboost.me/*
// @match        *://*.rekonise.com/*
// @match        *://*.letsboost.net/*
// @match        *://*.socialwolvez.com/*
// @match        *://*.work.ink/*
// @match        *://*.workink.net/*
// @match        *://*.fc.lc/*
// @match        *://*.fc-lc.com/*
// @match        *://*.cut-urls.com/*
// @match        *://*.exe.io/*
// @match        *://*.exey.io/*
// @match        *://*.sub2get.com/*
// @match        *://*.sub4unlock.io/*
// @match        *://*.earnow.online/*
// @match        *://*.gtamodding.com/*
// @match        *://*.gtamodding.net/*
// @match        *://*.gtaall.com/*
// @match        *://*.gtaall.net/*
// @match        *://*.modsfire.com/*
// @match        *://*.modsbase.com/*
// @match        *://*.up-load.io/*
// @match        *://*.upload.ee/*
// @match        *://*.ddownload.com/*
// @match        *://*.dbree.org/*
// @match        *://*.krakenfiles.com/*
// @match        *://*.1fichier.com/*
// @match        *://*.filecrypt.cc/*
// @match        *://*.filecrypt.co/*
// @grant        GM_xmlhttpRequest
// @grant        GM_notification
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        window.close
// @run-at       document-start
// @updateURL    https://raw.githubusercontent.com/ad-link-bypasser/userscript/main/ad-link-bypasser.user.js
// @downloadURL  https://raw.githubusercontent.com/ad-link-bypasser/userscript/main/ad-link-bypasser.user.js
// @supportURL   https://github.com/ad-link-bypasser/userscript/issues
// ==/UserScript==

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        debug: false,
        maxRetries: 3,
        retryDelay: 2000,
        timeout: 30000,
        showNotifications: true
    };

    // Utility functions
    const utils = {
        log: (message, type = 'info') => {
            if (CONFIG.debug) {
                console[type](`[Ad-Link Bypasser] ${message}`);
            }
        },

        notify: (message, type = 'info') => {
            if (CONFIG.showNotifications && typeof GM_notification !== 'undefined') {
                GM_notification({
                    title: 'Ad-Link Bypasser',
                    text: message,
                    timeout: 3000,
                    onclick: () => window.focus()
                });
            }
            utils.log(message, type);
        },

        sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

        waitForElement: (selector, timeout = 10000) => {
            return new Promise((resolve, reject) => {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                    return;
                }

                const observer = new MutationObserver((mutations, obs) => {
                    const element = document.querySelector(selector);
                    if (element) {
                        obs.disconnect();
                        resolve(element);
                    }
                });

                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                setTimeout(() => {
                    observer.disconnect();
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                }, timeout);
            });
        },

        extractUrlFromText: (text) => {
            const urlRegex = /(https?:\/\/[^\s<>"{}|\\^`[\]]+)/gi;
            const matches = text.match(urlRegex);
            return matches ? matches[0] : null;
        },

        isValidUrl: (string) => {
            try {
                new URL(string);
                return true;
            } catch (_) {
                return false;
            }
        },

        redirect: (url) => {
            if (utils.isValidUrl(url)) {
                utils.notify(`Redirecting to: ${url}`, 'info');
                window.location.href = url;
                return true;
            }
            return false;
        },

        waitForCloudflareBypass: () => {
            return new Promise((resolve) => {
                let attempts = 0;
                const maxAttempts = 30; // 30 seconds max wait

                const checkInterval = setInterval(() => {
                    attempts++;

                    // Check if we're still on a challenge page
                    const isChallengePage = document.title.includes('blocked') ||
                                          document.body.textContent.includes('Cloudflare') ||
                                          document.body.textContent.includes('security service') ||
                                          document.body.textContent.includes('checking your browser');

                    if (!isChallengePage || attempts >= maxAttempts) {
                        clearInterval(checkInterval);
                        resolve(!isChallengePage);
                    }
                }, 1000);
            });
        },

        detectCloudflareChallenge: () => {
            const indicators = [
                'Cloudflare',
                'security service',
                'checking your browser',
                'DDoS protection',
                'Ray ID',
                'cf-ray'
            ];

            const pageText = document.body.textContent.toLowerCase();
            const pageTitle = document.title.toLowerCase();

            return indicators.some(indicator =>
                pageText.includes(indicator.toLowerCase()) ||
                pageTitle.includes(indicator.toLowerCase())
            );
        }
    };

    // Bypass methods for different services
    const bypassMethods = {
        // Linkvertise bypass
        linkvertise: async () => {
            utils.log('Attempting Linkvertise bypass');

            try {
                // Check if we're on a Cloudflare challenge page
                if (document.title.includes('blocked') ||
                    document.body.textContent.includes('Cloudflare') ||
                    document.body.textContent.includes('security service')) {
                    utils.notify('Cloudflare protection detected - waiting for challenge completion', 'warning');

                    // Wait for Cloudflare challenge to complete
                    await utils.waitForCloudflareBypass();
                    return false; // Let the page reload naturally
                }

                // Method 1: Check for direct link in page source
                const pageContent = document.documentElement.innerHTML;
                const linkMatch = pageContent.match(/(?:target|destination)["']?\s*:\s*["']([^"']+)["']/i);

                if (linkMatch && linkMatch[1]) {
                    const decodedUrl = decodeURIComponent(linkMatch[1]);
                    if (utils.isValidUrl(decodedUrl)) {
                        return utils.redirect(decodedUrl);
                    }
                }

                // Method 2: Look for data attributes
                const linkElements = document.querySelectorAll('[data-target], [data-url], [data-link]');
                for (const element of linkElements) {
                    const url = element.dataset.target || element.dataset.url || element.dataset.link;
                    if (url && utils.isValidUrl(url)) {
                        return utils.redirect(url);
                    }
                }

                // Method 3: Check for bypass URLs in various formats
                const bypassPatterns = [
                    /window\.location\.href\s*=\s*["']([^"']+)["']/gi,
                    /location\.replace\(["']([^"']+)["']\)/gi,
                    /href\s*=\s*["']([^"']+)["']/gi
                ];

                for (const pattern of bypassPatterns) {
                    const matches = pageContent.matchAll(pattern);
                    for (const match of matches) {
                        const url = match[1];
                        if (url && utils.isValidUrl(url) && !url.includes('linkvertise')) {
                            return utils.redirect(url);
                        }
                    }
                }

                // Method 4: API bypass attempt with better error handling
                const currentUrl = window.location.href;
                const linkId = currentUrl.match(/\/(\d+)/)?.[1];

                if (linkId) {
                    const apiUrl = `https://publisher.linkvertise.com/api/v1/redirect/link/${linkId}`;

                    return new Promise((resolve) => {
                        GM_xmlhttpRequest({
                            method: 'GET',
                            url: apiUrl,
                            headers: {
                                'User-Agent': navigator.userAgent,
                                'Referer': currentUrl,
                                'Accept': 'application/json'
                            },
                            timeout: 10000,
                            onload: (response) => {
                                try {
                                    if (response.status === 200) {
                                        const data = JSON.parse(response.responseText);
                                        if (data.data && data.data.target) {
                                            resolve(utils.redirect(data.data.target));
                                            return;
                                        }
                                    }
                                } catch (e) {
                                    utils.log('API bypass failed: ' + e.message, 'error');
                                }
                                resolve(false);
                            },
                            onerror: () => {
                                utils.log('API request failed', 'error');
                                resolve(false);
                            },
                            ontimeout: () => {
                                utils.log('API request timed out', 'error');
                                resolve(false);
                            }
                        });
                    });
                }

                return false;
            } catch (error) {
                utils.log(`Linkvertise bypass error: ${error.message}`, 'error');
                return false;
            }
        },

        // Lootbox bypass
        lootbox: async () => {
            utils.log('Attempting Lootbox bypass');
            
            try {
                // Wait for the page to load
                await utils.sleep(2000);

                // Look for the final URL in various places
                const selectors = [
                    'a[href*="http"]:not([href*="loot"])',
                    '.btn[href*="http"]',
                    '[data-url]',
                    '.download-link'
                ];

                for (const selector of selectors) {
                    const element = document.querySelector(selector);
                    if (element) {
                        const url = element.href || element.dataset.url;
                        if (url && utils.isValidUrl(url) && !url.includes('loot')) {
                            return utils.redirect(url);
                        }
                    }
                }

                // Check page content for URLs
                const pageText = document.body.innerText;
                const extractedUrl = utils.extractUrlFromText(pageText);
                if (extractedUrl && !extractedUrl.includes('loot')) {
                    return utils.redirect(extractedUrl);
                }

                return false;
            } catch (error) {
                utils.log(`Lootbox bypass error: ${error.message}`, 'error');
                return false;
            }
        },

        // AdFly bypass
        adfly: async () => {
            utils.log('Attempting AdFly bypass');
            
            try {
                // AdFly stores the URL in a specific format
                const scripts = document.querySelectorAll('script');
                
                for (const script of scripts) {
                    const content = script.textContent;
                    
                    // Look for the encoded URL pattern
                    const match = content.match(/var ysmm = '([^']+)'/);
                    if (match) {
                        const encoded = match[1];
                        let decoded = '';
                        
                        for (let i = 0; i < encoded.length; i += 2) {
                            decoded += String.fromCharCode(parseInt(encoded.substring(i, i + 2), 16));
                        }
                        
                        if (utils.isValidUrl(decoded)) {
                            return utils.redirect(decoded);
                        }
                    }
                }

                return false;
            } catch (error) {
                utils.log(`AdFly bypass error: ${error.message}`, 'error');
                return false;
            }
        },

        // Shorte.st bypass
        shorte: async () => {
            utils.log('Attempting Shorte.st bypass');

            try {
                // Wait for timer to appear
                await utils.sleep(1000);

                // Skip timer by directly accessing the continue URL
                const continueBtn = document.querySelector('#skip_button, .skip-btn, .btn-primary');
                if (continueBtn && continueBtn.href) {
                    return utils.redirect(continueBtn.href);
                }

                // Look for hidden form with destination
                const forms = document.querySelectorAll('form');
                for (const form of forms) {
                    const urlInput = form.querySelector('input[name="url"], input[name="target"]');
                    if (urlInput && urlInput.value) {
                        return utils.redirect(urlInput.value);
                    }
                }

                return false;
            } catch (error) {
                utils.log(`Shorte.st bypass error: ${error.message}`, 'error');
                return false;
            }
        },

        // Ouo.io bypass
        ouo: async () => {
            utils.log('Attempting Ouo.io bypass');

            try {
                // Wait for page elements to load
                await utils.sleep(2000);

                // Look for the form with the next step
                const form = document.querySelector('form[action*="go"]');
                if (form) {
                    const tokenInput = form.querySelector('input[name="_token"]');
                    if (tokenInput) {
                        // Submit the form programmatically
                        form.submit();
                        return true;
                    }
                }

                // Alternative: look for direct links
                const links = document.querySelectorAll('a[href*="http"]:not([href*="ouo"])');
                for (const link of links) {
                    if (link.href && utils.isValidUrl(link.href)) {
                        return utils.redirect(link.href);
                    }
                }

                return false;
            } catch (error) {
                utils.log(`Ouo.io bypass error: ${error.message}`, 'error');
                return false;
            }
        },

        // Boost.ink and similar services bypass
        boost: async () => {
            utils.log('Attempting Boost.ink bypass');

            try {
                await utils.sleep(2000);

                // Look for the final URL in page data
                const scripts = document.querySelectorAll('script');
                for (const script of scripts) {
                    const content = script.textContent;
                    const urlMatch = content.match(/(?:url|link|target)["']?\s*[:=]\s*["']([^"']+)["']/i);
                    if (urlMatch && utils.isValidUrl(urlMatch[1])) {
                        return utils.redirect(urlMatch[1]);
                    }
                }

                // Check for data attributes on buttons
                const buttons = document.querySelectorAll('button, .btn, a');
                for (const button of buttons) {
                    const url = button.dataset.url || button.dataset.link || button.dataset.target;
                    if (url && utils.isValidUrl(url)) {
                        return utils.redirect(url);
                    }
                }

                return false;
            } catch (error) {
                utils.log(`Boost.ink bypass error: ${error.message}`, 'error');
                return false;
            }
        },

        // Sub2Unlock bypass
        sub2unlock: async () => {
            utils.log('Attempting Sub2Unlock bypass');

            try {
                await utils.sleep(3000);

                // Look for unlock button or direct link
                const unlockBtn = document.querySelector('.unlock-btn, .btn-unlock, #unlock');
                if (unlockBtn) {
                    unlockBtn.click();
                    await utils.sleep(1000);
                }

                // Check for revealed links
                const revealedLinks = document.querySelectorAll('.revealed-link, .unlocked-link, .final-link');
                for (const link of revealedLinks) {
                    if (link.href && utils.isValidUrl(link.href)) {
                        return utils.redirect(link.href);
                    }
                }

                return false;
            } catch (error) {
                utils.log(`Sub2Unlock bypass error: ${error.message}`, 'error');
                return false;
            }
        },

        // Generic bypass for other services
        generic: async () => {
            utils.log('Attempting generic bypass');

            try {
                await utils.sleep(3000);

                // Common selectors for download/continue buttons
                const buttonSelectors = [
                    '.btn-primary',
                    '.download-btn',
                    '.continue-btn',
                    '.get-link',
                    '.btn-success',
                    '.btn-download',
                    '.direct-link',
                    'button[onclick*="http"]',
                    'a[href*="http"]:not([href*="' + window.location.hostname + '"])'
                ];

                for (const selector of buttonSelectors) {
                    const element = document.querySelector(selector);
                    if (element) {
                        const url = element.href || element.dataset.url ||
                                   element.onclick?.toString().match(/https?:\/\/[^\s'"]+/)?.[0];
                        if (url && utils.isValidUrl(url) && !url.includes(window.location.hostname)) {
                            return utils.redirect(url);
                        }
                    }
                }

                // Auto-click continue buttons
                const continueButtons = document.querySelectorAll(
                    'button:contains("Continue"), button:contains("Skip"), .skip-btn, #skip'
                );
                for (const button of continueButtons) {
                    if (button.offsetParent !== null) { // Check if visible
                        button.click();
                        await utils.sleep(2000);
                        break;
                    }
                }

                return false;
            } catch (error) {
                utils.log(`Generic bypass error: ${error.message}`, 'error');
                return false;
            }
        }
    };

    // Main bypass logic
    const attemptBypass = async () => {
        const hostname = window.location.hostname.toLowerCase();
        utils.log(`Starting bypass for: ${hostname}`);

        // Check for Cloudflare protection first
        if (utils.detectCloudflareChallenge()) {
            utils.notify('Cloudflare protection detected - waiting for completion...', 'warning');
            utils.log('Cloudflare challenge detected, waiting for bypass');

            // Wait for Cloudflare challenge to complete
            const challengePassed = await utils.waitForCloudflareBypass();

            if (challengePassed) {
                utils.notify('Cloudflare challenge completed - retrying bypass', 'info');
                // Retry after challenge completion
                setTimeout(attemptBypass, 2000);
                return;
            } else {
                utils.notify('Cloudflare challenge timeout - manual completion required', 'warning');
                return;
            }
        }

        utils.notify(`Attempting to bypass ${hostname}`, 'info');

        let success = false;
        let retries = 0;

        while (!success && retries < CONFIG.maxRetries) {
            try {
                // Determine which bypass method to use based on hostname
                if (hostname.includes('linkvertise')) {
                    success = await bypassMethods.linkvertise();
                } else if (hostname.includes('loot')) {
                    success = await bypassMethods.lootbox();
                } else if (hostname.includes('adfly') || hostname.includes('adf.ly')) {
                    success = await bypassMethods.adfly();
                } else if (hostname.includes('shorte') || hostname.includes('sh.st')) {
                    success = await bypassMethods.shorte();
                } else if (hostname.includes('ouo')) {
                    success = await bypassMethods.ouo();
                } else if (hostname.includes('boost') || hostname.includes('work.ink') || hostname.includes('workink')) {
                    success = await bypassMethods.boost();
                } else if (hostname.includes('sub2unlock') || hostname.includes('sub2get') || hostname.includes('sub4unlock')) {
                    success = await bypassMethods.sub2unlock();
                } else {
                    success = await bypassMethods.generic();
                }

                if (!success) {
                    retries++;
                    if (retries < CONFIG.maxRetries) {
                        utils.log(`Bypass attempt ${retries} failed, retrying in ${CONFIG.retryDelay}ms`);
                        await utils.sleep(CONFIG.retryDelay);
                    }
                }
            } catch (error) {
                utils.log(`Bypass attempt failed: ${error.message}`, 'error');
                retries++;
                if (retries < CONFIG.maxRetries) {
                    await utils.sleep(CONFIG.retryDelay);
                }
            }
        }

        if (!success) {
            utils.notify('Bypass failed - manual interaction may be required', 'warning');
            utils.log('All bypass attempts failed');
        }
    };

    // Additional utility functions for enhanced bypass
    const enhancedUtils = {
        // Remove annoying overlays and popups
        removeOverlays: () => {
            const overlaySelectors = [
                '.overlay',
                '.popup',
                '.modal',
                '.advertisement',
                '.ad-banner',
                '[style*="position: fixed"]',
                '[style*="z-index: 999"]'
            ];

            overlaySelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    if (el.offsetHeight > window.innerHeight * 0.5 ||
                        el.offsetWidth > window.innerWidth * 0.5) {
                        el.style.display = 'none';
                    }
                });
            });
        },

        // Auto-close annoying popups
        closePopups: () => {
            const closeSelectors = [
                '.close',
                '.close-btn',
                '.modal-close',
                '[aria-label="Close"]',
                'button:contains("×")',
                'button:contains("Close")'
            ];

            closeSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    if (el.offsetParent !== null) {
                        el.click();
                    }
                });
            });
        },

        // Skip countdown timers
        skipTimers: () => {
            // Look for timer elements and try to skip them
            const timerSelectors = [
                '.timer',
                '.countdown',
                '#timer',
                '#countdown',
                '[id*="timer"]',
                '[class*="timer"]'
            ];

            timerSelectors.forEach(selector => {
                const timer = document.querySelector(selector);
                if (timer) {
                    // Try to set timer to 0 or 1
                    if (timer.textContent.match(/\d+/)) {
                        timer.textContent = '0';
                    }
                }
            });

            // Try to trigger timer completion events
            const events = ['timer-complete', 'countdown-finished', 'timer-end'];
            events.forEach(eventName => {
                document.dispatchEvent(new CustomEvent(eventName));
            });
        }
    };

    // Initialize the script
    const init = () => {
        utils.log('Ad-Link Bypasser v2.1.0 initialized');
        utils.notify('Ad-Link Bypasser activated', 'info');

        // Remove overlays and popups immediately
        enhancedUtils.removeOverlays();
        enhancedUtils.closePopups();

        // Set up periodic cleanup
        setInterval(() => {
            enhancedUtils.removeOverlays();
            enhancedUtils.closePopups();
            enhancedUtils.skipTimers();
        }, 2000);

        // Start bypass attempt after page loads
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(attemptBypass, 1000);
            });
        } else {
            setTimeout(attemptBypass, 1000);
        }

        // Add keyboard shortcut for manual bypass trigger (Ctrl+Shift+B)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'B') {
                e.preventDefault();
                utils.notify('Manual bypass triggered', 'info');
                attemptBypass();
            }
        });
    };

    // Start the script
    init();

})();
