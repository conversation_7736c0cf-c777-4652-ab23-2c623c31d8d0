// ==UserScript==
// @name         Cloudflare Bypass Helper for Ad-Link Services
// @namespace    https://github.com/ad-link-bypasser
// @version      1.0.0
// @description  Helper script to handle Cloudflare-protected ad-link shorteners
// <AUTHOR>
// @match        *://*.linkvertise.com/*
// @match        *://*.linkvertise.net/*
// @match        *://*.lootbox.to/*
// @match        *://*.adfly.com/*
// @match        *://*.shorte.st/*
// @match        *://*.ouo.io/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_notification
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        debug: true,
        maxWaitTime: 60000, // 60 seconds max wait for Cloudflare
        checkInterval: 1000, // Check every second
        retryDelay: 3000
    };

    // Utility functions
    const utils = {
        log: (message, type = 'info') => {
            if (CONFIG.debug) {
                console[type](`[CF Bypass Helper] ${message}`);
            }
        },

        notify: (message) => {
            if (typeof GM_notification !== 'undefined') {
                GM_notification({
                    title: 'Cloudflare Bypass Helper',
                    text: message,
                    timeout: 4000
                });
            }
            utils.log(message);
        },

        sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

        isCloudflareChallenge: () => {
            const indicators = [
                'checking your browser',
                'cloudflare',
                'ddos protection',
                'security check',
                'please wait',
                'ray id',
                'cf-ray'
            ];

            const pageText = document.body ? document.body.textContent.toLowerCase() : '';
            const pageTitle = document.title.toLowerCase();
            const pageHTML = document.documentElement.innerHTML.toLowerCase();

            return indicators.some(indicator => 
                pageText.includes(indicator) || 
                pageTitle.includes(indicator) ||
                pageHTML.includes(indicator)
            ) || document.querySelector('#cf-wrapper, .cf-browser-verification, .cf-checking-browser');
        },

        waitForPageLoad: () => {
            return new Promise((resolve) => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    window.addEventListener('load', resolve);
                }
            });
        }
    };

    // Alternative bypass methods for Cloudflare-protected sites
    const cloudflareBypass = {
        // Method 1: Wait for automatic redirect
        waitForRedirect: async () => {
            utils.log('Waiting for automatic redirect...');
            
            let startTime = Date.now();
            let lastUrl = window.location.href;
            
            return new Promise((resolve) => {
                const checkRedirect = setInterval(() => {
                    const currentTime = Date.now();
                    const currentUrl = window.location.href;
                    
                    // Check if URL changed (redirect happened)
                    if (currentUrl !== lastUrl) {
                        clearInterval(checkRedirect);
                        utils.log('Redirect detected');
                        resolve(true);
                        return;
                    }
                    
                    // Check if we're no longer on a challenge page
                    if (!utils.isCloudflareChallenge()) {
                        clearInterval(checkRedirect);
                        utils.log('Challenge completed');
                        resolve(true);
                        return;
                    }
                    
                    // Timeout check
                    if (currentTime - startTime > CONFIG.maxWaitTime) {
                        clearInterval(checkRedirect);
                        utils.log('Timeout waiting for redirect');
                        resolve(false);
                    }
                    
                    lastUrl = currentUrl;
                }, CONFIG.checkInterval);
            });
        },

        // Method 2: Look for hidden forms or meta redirects
        findHiddenRedirects: () => {
            utils.log('Looking for hidden redirects...');
            
            // Check for meta refresh
            const metaRefresh = document.querySelector('meta[http-equiv="refresh"]');
            if (metaRefresh) {
                const content = metaRefresh.getAttribute('content');
                const urlMatch = content.match(/url=(.+)/i);
                if (urlMatch) {
                    const redirectUrl = urlMatch[1];
                    utils.log(`Found meta redirect: ${redirectUrl}`);
                    window.location.href = redirectUrl;
                    return true;
                }
            }

            // Check for hidden forms
            const forms = document.querySelectorAll('form[style*="display:none"], form[style*="display: none"]');
            for (const form of forms) {
                if (form.action && form.action !== window.location.href) {
                    utils.log(`Found hidden form: ${form.action}`);
                    form.submit();
                    return true;
                }
            }

            // Check for JavaScript redirects in script tags
            const scripts = document.querySelectorAll('script');
            for (const script of scripts) {
                const content = script.textContent;
                const redirectMatch = content.match(/(?:window\.location|location\.href)\s*=\s*["']([^"']+)["']/);
                if (redirectMatch) {
                    const redirectUrl = redirectMatch[1];
                    if (redirectUrl.startsWith('http') && !redirectUrl.includes(window.location.hostname)) {
                        utils.log(`Found JS redirect: ${redirectUrl}`);
                        window.location.href = redirectUrl;
                        return true;
                    }
                }
            }

            return false;
        },

        // Method 3: Auto-click continue buttons
        autoClickContinue: () => {
            utils.log('Looking for continue buttons...');
            
            const buttonSelectors = [
                'button:contains("Continue")',
                'button:contains("Proceed")',
                'input[type="submit"]',
                '.btn-continue',
                '.continue-btn',
                '#continue',
                'a[href*="continue"]'
            ];

            for (const selector of buttonSelectors) {
                const button = document.querySelector(selector);
                if (button && button.offsetParent !== null) {
                    utils.log(`Clicking continue button: ${selector}`);
                    button.click();
                    return true;
                }
            }

            return false;
        }
    };

    // Main execution
    const init = async () => {
        utils.log('Cloudflare Bypass Helper initialized');
        
        // Wait for page to load
        await utils.waitForPageLoad();
        
        // Initial delay to let page settle
        await utils.sleep(2000);
        
        // Check if we're on a Cloudflare challenge page
        if (utils.isCloudflareChallenge()) {
            utils.notify('Cloudflare challenge detected - attempting bypass...');
            
            // Try different bypass methods
            let success = false;
            
            // Method 1: Look for immediate redirects
            success = cloudflareBypass.findHiddenRedirects();
            if (success) return;
            
            // Method 2: Try auto-clicking continue buttons
            await utils.sleep(1000);
            success = cloudflareBypass.autoClickContinue();
            if (success) return;
            
            // Method 3: Wait for automatic completion
            utils.notify('Waiting for Cloudflare challenge to complete...');
            success = await cloudflareBypass.waitForRedirect();
            
            if (success) {
                utils.notify('Cloudflare bypass successful!');
                // Trigger the main bypass script after a delay
                setTimeout(() => {
                    const event = new CustomEvent('cloudflare-bypassed');
                    document.dispatchEvent(event);
                }, 2000);
            } else {
                utils.notify('Cloudflare bypass failed - manual completion required');
            }
        } else {
            utils.log('No Cloudflare challenge detected');
        }
    };

    // Handle page navigation
    let lastUrl = window.location.href;
    const observer = new MutationObserver(() => {
        if (window.location.href !== lastUrl) {
            lastUrl = window.location.href;
            setTimeout(init, 1000);
        }
    });

    // Start observing
    if (document.body) {
        observer.observe(document.body, { childList: true, subtree: true });
    } else {
        document.addEventListener('DOMContentLoaded', () => {
            observer.observe(document.body, { childList: true, subtree: true });
        });
    }

    // Initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
